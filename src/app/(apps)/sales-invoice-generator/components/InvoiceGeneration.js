import { motion } from "framer-motion";
import { toast } from "react-hot-toast";
import { useEffect, useState } from "react";
import { API_ENDPOINTS } from "@/config/api";
import api, { downloadService } from "@/services/api";
import { TbArrowLeft, TbDownload, TbFileZip, TbMail } from "react-icons/tb";

const InvoiceGeneration = ({ onBack, variants, csvData, columnMappings, companyTemplate, onSendEmails, workflow }) => {
  const [loading, setLoading] = useState(false);
  const [generatedInvoices, setGeneratedInvoices] = useState([]);
  const [error, setError] = useState("");
  const [progress, setProgress] = useState(0);
  const [currentStep, setCurrentStep] = useState("preparing"); // preparing, generating, completed
  const [zipDownloadUrl, setZipDownloadUrl] = useState("");
  const [filePaths, setFilePaths] = useState([]);

  useEffect(() => {
    // Start invoice generation automatically when component mounts
    generateInvoices();
  }, []);

  const generateInvoices = async () => {
    setLoading(true);
    setError("");
    setCurrentStep("preparing");
    setProgress(10);

    try {
      // Validate required data before sending request
      if (!csvData?.file) {
        throw new Error("CSV file is required for invoice generation");
      }

      if (!columnMappings || Object.keys(columnMappings).length === 0) {
        throw new Error("Column mappings are required for invoice generation");
      }

      // Prepare the data for invoice generation using FormData to send the actual file
      const formData = new FormData();
      formData.append('file', csvData.file);
      formData.append('column_mappings', JSON.stringify(columnMappings));

      if (workflow?.id) {
        // Use workflow-based approach (supports both saved and temporary templates)
        formData.append('workflow_id', workflow.id);
        console.log("Invoice generation request data (workflow-based):", {
          file_name: csvData.file?.name,
          file_size: csvData.file?.size,
          column_mappings_count: Object.keys(columnMappings).length,
          workflow_id: workflow.id,
          is_temporary: workflow.is_temporary_template
        });
      } else if (companyTemplate?.id) {
        // Fallback to legacy company template approach
        formData.append('company_template_id', companyTemplate.id);
        formData.append('template_id', companyTemplate.template_id || '');
        console.log("Invoice generation request data (legacy):", {
          file_name: csvData.file?.name,
          file_size: csvData.file?.size,
          column_mappings_count: Object.keys(columnMappings).length,
          company_template_id: companyTemplate.id,
          template_id: companyTemplate.template_id
        });
      } else {
        throw new Error("Either workflow or company template is required for invoice generation");
      }

      setCurrentStep("generating");
      setProgress(30);

      // Call the backend API to generate invoices
      const response = await api.post(API_ENDPOINTS.GENERATE_INVOICES, formData, {
        headers: {
          'Content-Type': 'multipart/form-data'
        }
      });

      if (response.data.success) {
        setGeneratedInvoices(response.data.generated_invoices);
        setZipDownloadUrl(response.data.zip_download_url);
        setFilePaths(response.data.file_paths || []);
        setCurrentStep("completed");
        setProgress(100);
        toast.success(`Successfully generated ${response.data.total_generated} invoices!`);
      } else {
        throw new Error(response.data.error || "Failed to generate invoices");
      }

    } catch (err) {
      console.error("Error generating invoices:", err);
      setError(err.response?.data?.error || err.message || "Failed to generate invoices. Please try again.");
      setCurrentStep("error");
    } finally {
      setLoading(false);
    }
  };



  const handleDownloadInvoice = async (invoice) => {
    try {
      await downloadService.downloadInvoice(invoice.download_url);
      toast.success(`Downloaded ${invoice.filename}`);
    } catch (error) {
      console.error('Download failed:', error);
      toast.error(`Failed to download invoice: ${error.message}`);
    }
  };

  const handleDownloadAllZip = async () => {
    if (zipDownloadUrl) {
      try {
        // Pass the file paths to download only the current batch of invoices
        await downloadService.downloadZip(zipDownloadUrl, filePaths);
        toast.success('Downloaded current batch of invoices as ZIP');
      } catch (error) {
        console.error('ZIP download failed:', error);
        toast.error(`Failed to download ZIP: ${error.message}`);
      }
    }
  };

  const getStepIcon = (step) => {
    switch (step) {
      case "preparing":
        return (
          <svg className="animate-spin h-5 w-5 text-blue-600" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
            <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
            <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
          </svg>
        );
      case "generating":
        return (
          <svg className="animate-spin h-5 w-5 text-blue-600" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
            <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
            <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
          </svg>
        );
      case "completed":
        return (
          <svg className="h-5 w-5 text-green-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5 13l4 4L19 7" />
          </svg>
        );
      case "error":
        return (
          <svg className="h-5 w-5 text-red-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
          </svg>
        );
      default:
        return null;
    }
  };

  const getStepText = (step) => {
    switch (step) {
      case "preparing":
        return "Preparing invoice data...";
      case "generating":
        return "Generating PDF invoices...";
      case "completed":
        return "Invoice generation completed!";
      case "error":
        return "Error occurred during generation";
      default:
        return "";
    }
  };

  return (
    <motion.div
      className="flex flex-col gap-6"
      variants={variants}
    >
      {/* Header */}
      <div className="flex items-center justify-between">
        <div className="flex items-center gap-4">
          <motion.button
            onClick={onBack}
            className="w-12 h-12 rounded-full flex items-center justify-center bg-blue-600 text-white shadow-md hover:bg-blue-700 transition-colors"
            whileHover={{ scale: 1.05 }}
            whileTap={{ scale: 0.95 }}
            aria-label="Go back"
          >
            <TbArrowLeft className="h-6 w-6" />
          </motion.button>
          <div>
            <h2 className="text-xl font-medium text-gray-700">
              Generate Invoices
            </h2>
            <p className="text-gray-600 mt-1">
              Creating PDF invoices from your data using {companyTemplate?.template_display_name}
            </p>
          </div>
        </div>
      </div>

      {/* Progress Section */}
      <div className="bg-white border border-gray-200 rounded-lg p-6">
        <div className="flex items-center gap-3 mb-4">
          {getStepIcon(currentStep)}
          <div>
            <h3 className="font-medium text-gray-900">{getStepText(currentStep)}</h3>
            {currentStep !== "error" && (
              <p className="text-sm text-gray-600">
                {currentStep === "completed"
                  ? `Generated ${generatedInvoices.length} invoices successfully`
                  : "Please wait while we process your invoices..."
                }
              </p>
            )}
          </div>
        </div>

        {/* Progress Bar */}
        {currentStep !== "completed" && currentStep !== "error" && (
          <div className="w-full bg-gray-200 rounded-full h-2">
            <div
              className="bg-blue-600 h-2 rounded-full transition-all duration-500"
              style={{ width: `${progress}%` }}
            ></div>
          </div>
        )}

        {/* Error Message */}
        {error && (
          <div className="mt-4 bg-red-50 border border-red-200 rounded-lg p-3">
            <div className="flex items-start gap-2">
              <svg className="w-4 h-4 text-red-600 mt-0.5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
              </svg>
              <p className="text-sm text-red-800">{error}</p>
            </div>
          </div>
        )}
      </div>

      {/* Send Emails Section */}
      {currentStep === "completed" && generatedInvoices.length > 0 && onSendEmails && (
        <div className="bg-blue-50 border border-blue-200 rounded-lg p-6">
          <div className="flex items-center justify-between">
            <div>
              <h3 className="font-medium text-blue-900 mb-2">Send Invoices via Email</h3>
              <p className="text-sm text-blue-700">
                Send the generated invoices directly to your customers via email with customizable templates.
              </p>
            </div>
            <button
              onClick={() => onSendEmails(generatedInvoices)}
              className="flex items-center gap-2 px-6 py-3 bg-blue-600 text-white font-medium rounded-lg hover:bg-blue-700 transition-colors"
            >
              <TbMail className="h-5 w-5" />
              Send Emails
            </button>
          </div>
        </div>
      )}

      {/* Generated Invoices List */}
      {currentStep === "completed" && generatedInvoices.length > 0 && (
        <div className="bg-white border border-gray-200 rounded-lg p-6">
          <div className="flex items-center justify-between mb-4">
            <h3 className="font-medium text-gray-900">Generated Invoices</h3>
            <button
              onClick={handleDownloadAllZip}
              className="flex items-center gap-2 px-4 py-2 bg-green-600 text-white rounded-lg hover:bg-green-700 transition-colors"
            >
              <TbFileZip className="h-4 w-4" />
              Download All (ZIP)
            </button>
          </div>

          <div className="space-y-3">
            {generatedInvoices.map((invoice, index) => (
              <div key={index} className="flex items-center justify-between p-3 bg-gray-50 rounded-lg">
                <div>
                  <p className="font-medium text-gray-900">{invoice.client_name}</p>
                  <p className="text-sm text-gray-600">Invoice: {invoice.invoice_number}</p>
                  <p className="text-xs text-gray-500">{invoice.filename}</p>
                </div>
                <button
                  onClick={() => handleDownloadInvoice(invoice)}
                  className="flex items-center gap-2 px-3 py-1 bg-blue-600 text-white rounded-md hover:bg-blue-700 transition-colors text-sm"
                >
                  <TbDownload className="h-4 w-4" />
                  Download
                </button>
              </div>
            ))}
          </div>
        </div>
      )}

      {/* Retry Button */}
      {currentStep === "error" && (
        <div className="flex justify-center">
          <button
            onClick={generateInvoices}
            className="px-6 py-2 bg-blue-600 text-white font-medium rounded-lg hover:bg-blue-700 transition-colors"
          >
            Retry Generation
          </button>
        </div>
      )}
    </motion.div>
  );
};

export default InvoiceGeneration;
