from rest_framework import serializers
from .models import CompanyTemplate, SalesWorkflow, SalesInvoiceData


class CompanyTemplateSerializer(serializers.ModelSerializer):
    """Serializer for CompanyTemplate model."""

    class Meta:
        model = CompanyTemplate
        fields = [
            "id",
            "template_name",
            "template_id",
            "template_display_name",
            "company_name",
            "address_line_1",
            "address_line_2",
            "city",
            "state_province",
            "postal_code",
            "country",
            "phone",
            "email",
            "website",
            "default_payment_terms",
            "bank_name",
            "account_number",
            "routing_number",
            "swift_code",
            "tax_id",
            "business_registration",
            "logo_url",
            "created_at",
            "updated_at",
            "last_used",
        ]
        read_only_fields = ["id", "created_at", "updated_at"]

    def create(self, validated_data):
        # Set the user from the request context
        validated_data["user"] = self.context["request"].user
        return super().create(validated_data)


class CompanyTemplateListSerializer(serializers.ModelSerializer):
    """Simplified serializer for listing company templates."""

    class Meta:
        model = CompanyTemplate
        fields = [
            "id",
            "template_name",
            "template_display_name",
            "company_name",
            "created_at",
            "updated_at",
            "last_used",
        ]


class SalesWorkflowSerializer(serializers.ModelSerializer):
    """Serializer for SalesWorkflow model."""

    company_template_name = serializers.CharField(
        source="company_template.template_name", read_only=True, allow_null=True
    )
    company_name = serializers.CharField(
        source="company_template.company_name", read_only=True, allow_null=True
    )

    class Meta:
        model = SalesWorkflow
        fields = [
            "id",
            "status",
            "uploaded_file_name",
            "detected_columns",
            "column_mappings",
            "suggested_mappings",
            "total_rows",
            "processed_rows",
            "failed_rows",
            "error_message",
            "created_at",
            "updated_at",
            "completed_at",
            "company_template_name",
            "company_name",
            "is_temporary_template",
            "temporary_template_data",
        ]
        read_only_fields = ["id", "created_at", "updated_at"]

    def create(self, validated_data):
        # Set the user from the request context
        validated_data["user"] = self.context["request"].user
        return super().create(validated_data)


class SalesInvoiceDataSerializer(serializers.ModelSerializer):
    """Serializer for SalesInvoiceData model."""

    class Meta:
        model = SalesInvoiceData
        fields = [
            "id",
            "row_number",
            "invoice_data",
            "is_processed",
            "processing_error",
            "generated_invoice_path",
            "invoice_number",
            "created_at",
            "updated_at",
        ]
        read_only_fields = ["id", "created_at", "updated_at"]


class ColumnMappingRequestSerializer(serializers.Serializer):
    """Serializer for column mapping requests."""

    csv_columns = serializers.ListField(
        child=serializers.CharField(max_length=200),
        help_text="List of column names from the uploaded CSV file",
    )

    def validate_csv_columns(self, value):
        if not value:
            raise serializers.ValidationError("CSV columns list cannot be empty")
        return value


class ColumnMappingResponseSerializer(serializers.Serializer):
    """Serializer for column mapping responses."""

    suggested_mappings = serializers.DictField(
        child=serializers.CharField(max_length=200),
        help_text="Suggested mappings from invoice fields to CSV columns",
    )
    confidence_scores = serializers.DictField(
        child=serializers.FloatField(min_value=0.0, max_value=1.0),
        help_text="Confidence scores for each suggested mapping",
    )


class WorkflowStartRequestSerializer(serializers.Serializer):
    """Serializer for starting a new sales workflow."""

    company_template_id = serializers.IntegerField(
        required=False,
        allow_null=True,
        help_text="ID of the company template to use (optional for temporary workflows)",
    )

    # Fields for temporary template data
    is_temporary = serializers.BooleanField(
        default=False,
        help_text="True if this is a temporary workflow without saving template",
    )
    temporary_template_data = serializers.JSONField(
        required=False,
        allow_null=True,
        help_text="Template data for temporary workflows",
    )

    def validate(self, data):
        """Validate that either company_template_id or temporary template data is provided."""
        company_template_id = data.get("company_template_id")
        is_temporary = data.get("is_temporary", False)
        temporary_template_data = data.get("temporary_template_data")

        if not is_temporary and not company_template_id:
            raise serializers.ValidationError(
                "Either company_template_id must be provided or is_temporary must be True"
            )

        if is_temporary and not temporary_template_data:
            raise serializers.ValidationError(
                "temporary_template_data is required when is_temporary is True"
            )

        return data

    def validate_company_template_id(self, value):
        if value is None:
            return value

        user = self.context["request"].user
        try:
            CompanyTemplate.objects.get(id=value, user=user)
        except CompanyTemplate.DoesNotExist:
            raise serializers.ValidationError(
                "Company template not found or not accessible"
            )
        return value
